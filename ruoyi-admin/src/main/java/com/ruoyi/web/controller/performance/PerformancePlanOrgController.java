package com.ruoyi.web.controller.performance;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.PerformancePlanOrg;
import com.ruoyi.system.service.IPerformancePlanOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 组织绩效计划Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/performance/plan/org")
public class PerformancePlanOrgController extends BaseController {
    @Autowired
    private IPerformancePlanOrgService service;

    /**
     * 查询组织绩效计划列表
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:list')")
    @GetMapping("/list")
    public TableDataInfo list(PerformancePlanOrg planOrg) {
        startPage();
        List<PerformancePlanOrg> list = service.selectPerformancePlanOrgList(planOrg);
        return getDataTable(list);
    }

    /**
     * 下载模板
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:list')")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws Exception {
        service.downloadTemplate(response);
    }

    /**
     * 获取组织绩效计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(service.selectPerformancePlanOrgById(id));
    }

    /**
     * 新增组织绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:add')")
    @Log(title = "组织绩效计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PerformancePlanOrg planOrg) {
        return toAjax(service.insertPerformancePlanOrg(planOrg));
    }

    /**
     * 修改组织绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:edit')")
    @Log(title = "组织绩效计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PerformancePlanOrg planOrg) {
        return toAjax(service.updatePerformancePlanOrg(planOrg));
    }

    /**
     * 删除组织绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:remove')")
    @Log(title = "组织绩效计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deletePerformancePlanOrgByIds(ids));
    }

    /**
     * 导出单个Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:export')")
    @Log(title = "组织绩效计划", businessType = BusinessType.EXPORT)
    @GetMapping("/export/{id}")
    public void exportWord(@PathVariable("id") Long id, HttpServletResponse response) throws Exception {
        service.exportWord(new Long[]{id}, response);
    }

    /**
     * 批量导出Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:export')")
    @Log(title = "组织绩效计划", businessType = BusinessType.EXPORT)
    @PostMapping("/batchExport")
    public void batchExportWord(@RequestBody Long[] ids, HttpServletResponse response) throws Exception {
        service.exportWord(ids, response);
    }

    /**
     * 导入Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:import')")
    @Log(title = "组织绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importWord(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            service.importData(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导入Excel文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:import')")
    @Log(title = "组织绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importExcel")
    public AjaxResult importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            service.importExcelData(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载Excel模板
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:org:list')")
    @GetMapping("/downloadExcelTemplate")
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        service.downloadExcelTemplate(response);
    }

} 
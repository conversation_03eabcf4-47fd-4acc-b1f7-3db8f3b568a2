{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/index.vue", "mtime": 1754316341834}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RPcmdQbGFuLCBnZXRPcmdQbGFuLCBhZGRPcmdQbGFuLCB1cGRhdGVPcmdQbGFuLCBkZWxPcmdQbGFuIH0gZnJvbSAnQC9hcGkvcGVyZm9ybWFuY2Uvb3JnJwppbXBvcnQgeyBsaXN0UGxhbiwgZ2V0UGxhbiwgYWRkUGxhbiwgdXBkYXRlUGxhbiwgZGVsUGxhbiwgZXhwb3J0T3JnUGxhbiwgYmF0Y2hFeHBvcnRPcmdQbGFuLCBkb3dubG9hZE9yZ1RlbXBsYXRlIH0gZnJvbSAiQC9hcGkvcGVyZm9ybWFuY2UvcGxhbiIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnT3JnUGxhbicsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHBsYW5MaXN0OiBbXSwKICAgICAgdG90YWw6IDAsCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgZm9ybVZpc2libGU6IGZhbHNlLAogICAgICBmb3JtVGl0bGU6ICcnLAogICAgICBmb3JtOiB7CiAgICAgICAgaWQ6IHVuZGVmaW5lZCwKICAgICAgICBzZXE6ICcnLAogICAgICAgIHRhc2tUeXBlOiAnJywKICAgICAgICB0YXNrU291cmNlOiAnJywKICAgICAgICBwZXJmb3JtYW5jZVRhc2s6ICcnLAogICAgICAgIHRhcmdldE1lYXN1cmVzOiAnJywKICAgICAgICByZXNwb25zaWJsZURlcHQ6ICcnLAogICAgICAgIHZhbHVlV2VpZ2h0OiAnJywKICAgICAgICByZXNwb25zaWJsZUxlYWRlcjogJycsCiAgICAgICAgZGVhZGxpbmU6ICcnCiAgICAgIH0sCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIGlkczogW10sCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIHRpdGxlOiAnJywKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIGltcG9ydFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvcGVyZm9ybWFuY2UvcGxhbi9vcmcvaW1wb3J0RXhjZWwnLAogICAgICB1cGxvYWRIZWFkZXJzOiB7fSwKICAgICAgcnVsZXM6IHsKICAgICAgICB0YXNrVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS7u+WK<PERSON><PERSON>u+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0YXNrU291cmNlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh5p2l5rqQ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHBlcmZvcm1hbmNlVGFzazogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue7qeaViOS7u+WKoeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0YXJnZXRNZWFzdXJlczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuebruagh+WPiuaOquaWveS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICByZXNwb25zaWJsZURlcHQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLotKPku7vnp5HlrqTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdmFsdWVXZWlnaHQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLliIblgLzlj4rmnYPph43kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcmVzcG9uc2libGVMZWFkZXI6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLotKPku7vpooblr7zkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgZGVhZGxpbmU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrozmiJDml7bpmZDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgLy8g6K6+572u5LiK5Lyg6K6k6K+B5aS0CiAgICB0aGlzLnVwbG9hZEhlYWRlcnMgPSB7CiAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIHRoaXMuJHN0b3JlLmdldHRlcnMudG9rZW4KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIGxpc3RPcmdQbGFuKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnBsYW5MaXN0ID0gcmVzLnJvd3MgfHwgcmVzCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbCB8fCAocmVzLmxlbmd0aCB8fCAwKQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinue7qeaViOiuoeWIkicKICAgICAgdGhpcy5mb3JtID0geyBpZDogdW5kZWZpbmVkLCBzZXE6ICcnLCB0YXNrVHlwZTogJycsIHRhc2tTb3VyY2U6ICcnLCBwZXJmb3JtYW5jZVRhc2s6ICcnLCB0YXJnZXRNZWFzdXJlczogJycsIHJlc3BvbnNpYmxlRGVwdDogJycsIHZhbHVlV2VpZ2h0OiAnJywgcmVzcG9uc2libGVMZWFkZXI6ICcnLCBkZWFkbGluZTogJycgfQogICAgICB0aGlzLm9wZW4gPSB0cnVlCiAgICB9LAogICAgaGFuZGxlRWRpdChyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICfnvJbovpHnu6nmlYjorqHliJInCiAgICAgIHRoaXMuZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIHJvdykKICAgICAgdGhpcy5vcGVuID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTnu6nmlYjorqHliJLnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsT3JnUGxhbihpZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIGhhbmRsZUV4cG9ydFNlbGVjdGVkKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi6K+36YCJ5oup6KaB5a+85Ye655qE5pWw5o2uIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuumAieS4reeahCcgKyB0aGlzLmlkcy5sZW5ndGggKyAn5p2h57up5pWI6K6h5YiS5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5a+85Ye65pWw5o2u77yM6K+356iN5YCZLi4uIik7CiAgICAgICAgYmF0Y2hFeHBvcnRPcmdQbGFuKHRoaXMuaWRzKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2VdLCB7CiAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcKICAgICAgICAgIH0pOwogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICAgIGxpbmsuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICAgICAgbGluay5kb3dubG9hZCA9ICfnu4Tnu4fnu6nmlYjorqHliJJfJyArIG5ldyBEYXRlKCkuZ2V0VGltZSgpICsgJy5kb2N4JzsKICAgICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7CiAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWvvOWHuuaIkOWKnyIpOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWvvOWHuuWksei0pSIpOwogICAgICAgIH0pOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInnu6nmlYjorqHliJLmlbDmja7pobnvvJ8nKS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjlr7zlh7rmlbDmja7vvIzor7fnqI3lgJkuLi4iKTsKICAgICAgICBleHBvcnRPcmdQbGFuKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgeyB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIH0pOwogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICAgIGxpbmsuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICAgICAgbGluay5kb3dubG9hZCA9ICfnu4Tnu4fnu6nmlYjorqHliJIuZG9jeCc7CiAgICAgICAgICBsaW5rLmNsaWNrKCk7CiAgICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOwogICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBoYW5kbGVEb3dubG9hZFRlbXBsYXRlKCkgewogICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjkuIvovb3mqKHmnb/vvIzor7fnqI3lgJkuLi4iKTsKICAgICAgZG93bmxvYWRPcmdFeGNlbFRlbXBsYXRlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgdHlwZTogJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB9KTsKICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOwogICAgICAgIGxpbmsuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICAgIGxpbmsuZG93bmxvYWQgPSAn57uE57uH57up5pWI6K6h5YiS5qih5p2/Lnhsc3gnOwogICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOwogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgZ2V0UGxhbihpZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS557up5pWI6K6h5YiSIjsKICAgICAgfSk7CiAgICB9LAogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1sicGxhblJlZiJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQpIHsKICAgICAgICAgICAgdXBkYXRlUGxhbih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFBsYW4odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVJbXBvcnRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwIHx8IHJlc3BvbnNlID09PSAn5a+85YWl5oiQ5YqfJykgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WvvOWFpeaIkOWKnycpOwogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAn5a+85YWl5aSx6LSlJyk7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVJbXBvcnRFcnJvcihlcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCLlr7zlhaXlpLHotKU6IiwgZXJyb3IpOwogICAgICBpZiAoZXJyb3Iuc3RhdHVzID09PSA0MDEpIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K6k6K+B5aSx6LSl77yM6K+36YeN5paw55m75b2VIik7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ0xvZ091dCcpLnRoZW4oKCkgPT4gewogICAgICAgICAgbG9jYXRpb24uaHJlZiA9ICcvbG9naW4nOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlhaXlpLHotKXvvIzor7fmo4Dmn6Xmlofku7bmoLzlvI8iKTsKICAgICAgfQogICAgfSwKICAgIGJlZm9yZUltcG9ydFVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwKICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJwogICAgICBpZiAoIWlzRXhjZWwpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tiEnKQogICAgICB9CiAgICAgIHJldHVybiBpc0V4Y2VsCiAgICB9LAogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZQogICAgICB0aGlzLnJlc2V0KCkKICAgIH0sCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiB1bmRlZmluZWQsCiAgICAgICAgc2VxOiAnJywKICAgICAgICB0YXNrVHlwZTogJycsCiAgICAgICAgdGFza1NvdXJjZTogJycsCiAgICAgICAgcGVyZm9ybWFuY2VUYXNrOiAnJywKICAgICAgICB0YXJnZXRNZWFzdXJlczogJycsCiAgICAgICAgcmVzcG9uc2libGVEZXB0OiAnJywKICAgICAgICB2YWx1ZVdlaWdodDogJycsCiAgICAgICAgcmVzcG9uc2libGVMZWFkZXI6ICcnLAogICAgICAgIGRlYWRsaW5lOiAnJwogICAgICB9CiAgICAgIHRoaXMucmVzZXRGb3JtKCJwbGFuUmVmIikKICAgIH0sCiAgICBmb3JtYXRKc29uKGZpbHRlclZhbCwganNvbkRhdGEpIHsKICAgICAgcmV0dXJuIGpzb25EYXRhLm1hcCh2ID0+IGZpbHRlclZhbC5tYXAoaiA9PiB2W2pdKSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          style=\"display: inline-block;\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"planList\"\n      @selection-change=\"handleSelectionChange\"\n      row-key=\"id\"\n      border\n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" width=\"120\"/>\n      <el-table-column label=\"任务来源\" align=\"center\" prop=\"taskSource\" width=\"120\"/>\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column label=\"责任科室\" align=\"center\" prop=\"responsibleDept\" width=\"120\"/>\n      <el-table-column label=\"分值及权重\" align=\"center\" prop=\"valueWeight\" width=\"120\"/>\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" width=\"120\"/>\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"deadline\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template #default=\"scope\">\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            icon=\"Edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['performance:plan:org:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            icon=\"Delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:plan:org:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"planRef\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"任务类型\" prop=\"taskType\">\n          <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n        </el-form-item>\n        <el-form-item label=\"任务来源\" prop=\"taskSource\">\n          <el-input v-model=\"form.taskSource\" placeholder=\"请输入任务来源\" />\n        </el-form-item>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n          <el-input v-model=\"form.responsibleDept\" placeholder=\"请输入责任科室\" />\n        </el-form-item>\n        <el-form-item label=\"分值及权重\" prop=\"valueWeight\">\n          <el-input v-model=\"form.valueWeight\" placeholder=\"请输入分值及权重\" />\n        </el-form-item>\n        <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n          <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n        </el-form-item>\n        <el-form-item label=\"完成时限\" prop=\"deadline\">\n          <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"cancel\">取 消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOrgPlan, getOrgPlan, addOrgPlan, updateOrgPlan, delOrgPlan } from '@/api/performance/org'\nimport { listPlan, getPlan, addPlan, updatePlan, delPlan, exportOrgPlan, batchExportOrgPlan, downloadOrgTemplate } from \"@/api/performance/plan\"\n\nexport default {\n  name: 'OrgPlan',\n  data() {\n    return {\n      planList: [],\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      formVisible: false,\n      formTitle: '',\n      form: {\n        id: undefined,\n        seq: '',\n        taskType: '',\n        taskSource: '',\n        performanceTask: '',\n        targetMeasures: '',\n        responsibleDept: '',\n        valueWeight: '',\n        responsibleLeader: '',\n        deadline: ''\n      },\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      title: '',\n      open: false,\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/plan/org/importExcel',\n      uploadHeaders: {},\n      rules: {\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" }\n        ],\n        taskSource: [\n          { required: true, message: \"任务来源不能为空\", trigger: \"blur\" }\n        ],\n        performanceTask: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" }\n        ],\n        targetMeasures: [\n          { required: true, message: \"目标及措施不能为空\", trigger: \"blur\" }\n        ],\n        responsibleDept: [\n          { required: true, message: \"责任科室不能为空\", trigger: \"blur\" }\n        ],\n        valueWeight: [\n          { required: true, message: \"分值及权重不能为空\", trigger: \"blur\" }\n        ],\n        responsibleLeader: [\n          { required: true, message: \"责任领导不能为空\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { required: true, message: \"完成时限不能为空\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true\n      listOrgPlan(this.queryParams).then(res => {\n        this.planList = res.rows || res\n        this.total = res.total || (res.length || 0)\n        this.loading = false\n      })\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.title = '新增绩效计划'\n      this.form = { id: undefined, seq: '', taskType: '', taskSource: '', performanceTask: '', targetMeasures: '', responsibleDept: '', valueWeight: '', responsibleLeader: '', deadline: '' }\n      this.open = true\n    },\n    handleEdit(row) {\n      this.title = '编辑绩效计划'\n      this.form = Object.assign({}, row)\n      this.open = true\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除绩效计划编号为\"' + ids + '\"的数据项？').then(function() {\n        return delOrgPlan(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条绩效计划数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        batchExportOrgPlan(this.ids).then(response => {\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '组织绩效计划_' + new Date().getTime() + '.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n          this.$modal.msgSuccess(\"导出成功\");\n        }).catch(() => {\n          this.$modal.closeLoading();\n          this.$modal.msgError(\"导出失败\");\n        });\n      }).catch(() => {});\n    },\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有绩效计划数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        exportOrgPlan().then(response => {\n          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '组织绩效计划.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n        }).catch(() => {\n          this.$modal.closeLoading();\n        });\n      }).catch(() => {});\n    },\n    handleDownloadTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍候...\");\n      downloadOrgExcelTemplate().then(response => {\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '组织绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getPlan(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改绩效计划\";\n      });\n    },\n    submitForm() {\n      this.$refs[\"planRef\"].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n            updatePlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    handleImportSuccess(response, file, fileList) {\n      if (response.code === 200 || response === '导入成功') {\n        this.$modal.msgSuccess('导入成功');\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                     file.type === 'application/vnd.ms-excel'\n      if (!isExcel) {\n        this.$message.error('只能上传Excel文件!')\n      }\n      return isExcel\n    },\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    reset() {\n      this.form = {\n        id: undefined,\n        seq: '',\n        taskType: '',\n        taskSource: '',\n        performanceTask: '',\n        targetMeasures: '',\n        responsibleDept: '',\n        valueWeight: '',\n        responsibleLeader: '',\n        deadline: ''\n      }\n      this.resetForm(\"planRef\")\n    },\n    formatJson(filterVal, jsonData) {\n      return jsonData.map(v => filterVal.map(j => v[j]))\n    }\n  }\n}\n</script>\n\n<style scoped>\n.mb8 {\n  margin-bottom: 8px;\n}\n.upload-demo {\n  display: inline-block;\n}\n</style>\n"]}]}
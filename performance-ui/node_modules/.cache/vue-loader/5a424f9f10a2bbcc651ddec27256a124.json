{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue?vue&type=template&id=ee50d736&scoped=true", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue", "mtime": 1753513689245}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
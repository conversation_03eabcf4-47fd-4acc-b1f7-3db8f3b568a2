{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue", "mtime": 1753513689245}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdExlYWRlciwgZ2V0TGVhZGVyLCBhZGRMZWFkZXIsIHVwZGF0ZUxlYWRlciwgZGVsTGVhZGVyLAogIGV4cG9ydExlYWRlciwgYmF0Y2hFeHBvcnRMZWFkZXIsIGRvd25sb2FkVGVtcGxhdGUKfSBmcm9tICdAL2FwaS9wZXJmb3JtYW5jZS9sZWFkZXInCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkxlYWRlclBsYW4iLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICBpZHM6IFtdLAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdDogW10sCiAgICAgIHRpdGxlOiAiIiwKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICB0YXNrVHlwZTogbnVsbCwKICAgICAgICBvcmdOYW1lOiBudWxsLAogICAgICAgIHBsYW5ZZWFyOiBudWxsCiAgICAgIH0sCiAgICAgIGZvcm06IHt9LAogICAgICBydWxlczogewogICAgICAgIG5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLlp5PlkI3plb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdGFza1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLku7vliqHnsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLku7vliqHnsbvlnovplb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcGVyZm9ybWFuY2VUYXNrOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi57up5pWI5Lu75Yqh5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogMTAwMCwgbWVzc2FnZTogIue7qeaViOS7u+WKoemVv+W6puS4jeiDvei2hei/hzEwMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdGFyZ2V0TWVhc3VyZXM6IFsKICAgICAgICAgIHsgbWF4OiAxMDAwLCBtZXNzYWdlOiAi55uu5qCH5Y+K5o6q5pa96ZW/5bqm5LiN6IO96LaF6L+HMTAwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBldmFsdWF0aW9uQ3JpdGVyaWE6IFsKICAgICAgICAgIHsgbWF4OiAxMDAwLCBtZXNzYWdlOiAi6K+E5Lu35qCH5YeG6ZW/5bqm5LiN6IO96LaF6L+HMTAwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICByZXNwb25zaWJpbGl0eTogWwogICAgICAgICAgeyBtYXg6IDEwMCwgbWVzc2FnZTogIui0o+S7u+mVv+W6puS4jeiDvei2hei/hzEwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0YXNrQ2F0ZWdvcnk6IFsKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLotKPku7vliIbnsbvplb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgd2VpZ2h0U2NvcmU6IFsKICAgICAgICAgIHsgbWF4OiA1MCwgbWVzc2FnZTogIuadg+mHjeWIhuWAvOmVv+W6puS4jeiDvei2hei/hzUw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGRlYWRsaW5lOiBbCiAgICAgICAgICB7IG1heDogMTAwLCBtZXNzYWdlOiAi5a6M5oiQ5pe26ZmQ6ZW/5bqm5LiN6IO96LaF6L+HMTAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIG9yZ05hbWU6IFsKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLmiYDlsZ7nu4Tnu4fplb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcGxhblllYXI6IFsKICAgICAgICAgIHsgbWF4OiAxMCwgbWVzc2FnZTogIuiuoeWIkuW5tOS7vemVv+W6puS4jeiDvei2hei/hzEw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIGltcG9ydFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvcGVyZm9ybWFuY2UvbGVhZGVyL2ltcG9ydCcsCiAgICAgIGltcG9ydFBhcmFtczogeyBwbGFuWWVhcjogJycsIG9yZ05hbWU6ICcnIH0sCiAgICAgIHVwbG9hZEhlYWRlcnM6IHt9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICAvLyDorr7nva7kuIrkvKDorqTor4HlpLQKICAgIHRoaXMudXBsb2FkSGVhZGVycyA9IHsKICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RMZWFkZXIodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIHRhc2tUeXBlOiBudWxsLAogICAgICAgIHBlcmZvcm1hbmNlVGFzazogbnVsbCwKICAgICAgICB0YXJnZXRNZWFzdXJlczogbnVsbCwKICAgICAgICBldmFsdWF0aW9uQ3JpdGVyaWE6IG51bGwsCiAgICAgICAgcmVzcG9uc2liaWxpdHk6IG51bGwsCiAgICAgICAgdGFza0NhdGVnb3J5OiBudWxsLAogICAgICAgIHdlaWdodFNjb3JlOiBudWxsLAogICAgICAgIGRlYWRsaW5lOiBudWxsLAogICAgICAgIG9yZ05hbWU6IG51bGwsCiAgICAgICAgcGxhblllYXI6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg54+t5a2Q5oiQ5ZGY57up5pWI6K6h5YiSIjsKICAgIH0sCiAgICBoYW5kbGVFZGl0KHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkOwogICAgICBnZXRMZWFkZXIoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueePreWtkOaIkOWRmOe7qeaViOiuoeWIkiI7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W5pWw5o2u5aSx6LSlIik7CiAgICAgIH0pOwogICAgfSwKICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlTGVhZGVyKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5L+u5pS55aSx6LSlIik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkTGVhZGVyKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5paw5aKe5aSx6LSlIik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cgPyBbcm93LmlkXSA6IHRoaXMuaWRzOwogICAgICBjb25zdCBtZXNzYWdlID0gcm93CiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5Yig6ZmkIiR7cm93Lm5hbWV9IueahOe7qeaViOiuoeWIkuaVsOaNrumhue+8n2AKICAgICAgICA6IGDmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoQke2lkcy5sZW5ndGh95p2h54+t5a2Q5oiQ5ZGY57up5pWI6K6h5YiS5pWw5o2u6aG577yfYDsKCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0obWVzc2FnZSkudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsTGVhZGVyKGlkcy5qb2luKCcsJykpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIGhhbmRsZUV4cG9ydFNlbGVjdGVkKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeWvvOWHuueahOaVsOaNriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvLyDlpoLmnpzlj6rpgInmi6nkuobkuIDmnaHmlbDmja7vvIzkvb/nlKjljZXkuKrlr7zlh7oKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMSkgewogICAgICAgIGNvbnN0IHNlbGVjdGVkUm93ID0gdGhpcy5saXN0LmZpbmQoaXRlbSA9PiBpdGVtLmlkID09PSB0aGlzLmlkc1swXSk7CiAgICAgICAgdGhpcy5oYW5kbGVFeHBvcnRTaW5nbGUoc2VsZWN0ZWRSb3cpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvLyDlpJrmnaHmlbDmja7kvb/nlKjmibnph4/lr7zlh7oKICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5a+85Ye65pWw5o2u77yM6K+356iN5YCZLi4uIik7CiAgICAgIGJhdGNoRXhwb3J0TGVhZGVyKHRoaXMuaWRzKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgeyAKICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcgCiAgICAgICAgfSk7CiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICBsaW5rLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICBsaW5rLmRvd25sb2FkID0gJ+ePreWtkOaIkOWRmOe7qeaViOiuoeWIki5kb2N4JzsKICAgICAgICBsaW5rLmNsaWNrKCk7CiAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwobGluay5ocmVmKTsKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVFeHBvcnRTaW5nbGUocm93KSB7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWHuuaVsOaNru+8jOivt+eojeWAmS4uLiIpOwogICAgICBleHBvcnRMZWFkZXIocm93LmlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgeyAKICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcgCiAgICAgICAgfSk7CiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICBsaW5rLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICBsaW5rLmRvd25sb2FkID0gYCR7cm93Lm5hbWUgfHwgJ+ePreWtkOaIkOWRmCd9X+e7qeaViOiuoeWIki5kb2N4YDsKICAgICAgICBsaW5rLmNsaWNrKCk7CiAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwobGluay5ocmVmKTsKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICB9KTsKICAgIH0sCiAgICBiZWZvcmVJbXBvcnRVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBpc0RvYyA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50JzsKICAgICAgaWYgKCFpc0RvYykgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCflj6rog73kuIrkvKBXb3Jk5paH5qGj5qC85byP5paH5Lu2IScpOwogICAgICB9CiAgICAgIHJldHVybiBpc0RvYzsKICAgIH0sCiAgICBoYW5kbGVJbXBvcnRTdWNjZXNzKHJlc3BvbnNlKSB7CiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCflr7zlhaXmiJDlip8nKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcihyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeWksei0pScpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlSW1wb3J0RXJyb3IoZXJyb3IpIHsKICAgICAgY29uc29sZS5lcnJvcigi5a+85YWl5aSx6LSlOiIsIGVycm9yKTsKICAgICAgaWYgKGVycm9yLnN0YXR1cyA9PT0gNDAxKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiupOivgeWksei0pe+8jOivt+mHjeaWsOeZu+W9lSIpOwogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdMb2dPdXQnKS50aGVuKCgpID0+IHsKICAgICAgICAgIGxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJzsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5a+85YWl5aSx6LSl77yM6K+35qOA5p+l5paH5Lu25qC85byPIik7CiAgICAgIH0KICAgIH0sCiAgICBkb3dubG9hZFRlbXBsYXRlKCkgewogICAgICBkb3dubG9hZFRlbXBsYXRlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgCiAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIAogICAgICAgIH0pOwogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgbGluay5kb3dubG9hZCA9ICfnj63lrZDmiJDlkZjnu6nmlYjorqHliJLmqKHmnb8uZG9jeCc7CiAgICAgICAgbGluay5jbGljaygpOwogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7CiAgICAgIH0pOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/leader", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"姓名\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input\n          v-model=\"queryParams.taskType\"\n          placeholder=\"请输入任务类型\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['performance:leader:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['performance:leader:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\" v-hasPermi=\"['performance:leader:export']\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          :data=\"importParams\"\n          style=\"display: inline-block;\"\n          v-hasPermi=\"['performance:leader:import']\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Word</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"downloadTemplate\" v-hasPermi=\"['performance:leader:list']\">下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n    \n    <el-table \n      v-loading=\"loading\"\n      :data=\"list\" \n      @selection-change=\"handleSelectionChange\"\n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"100\"/>\n      <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\"/>\n      <el-table-column prop=\"performanceTask\" label=\"绩效任务\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"targetMeasures\" label=\"目标及措施\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"evaluationCriteria\" label=\"评价标准\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"taskCategory\" label=\"责任分类\" width=\"100\"/>\n      <el-table-column prop=\"weightScore\" label=\"权重分值\" width=\"100\"/>\n      <el-table-column prop=\"deadline\" label=\"完成时限\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n            v-hasPermi=\"['performance:leader:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:leader:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"姓名\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型/类别\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationCriteria\">\n          <el-input v-model=\"form.evaluationCriteria\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"责任\" prop=\"responsibility\">\n              <el-input v-model=\"form.responsibility\" placeholder=\"请输入责任\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"责任分类\" prop=\"taskCategory\">\n              <el-input v-model=\"form.taskCategory\" placeholder=\"请输入责任分类\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"权重分值\" prop=\"weightScore\">\n              <el-input v-model=\"form.weightScore\" placeholder=\"请输入权重分值\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"完成时限\" prop=\"deadline\">\n              <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属组织\" prop=\"orgName\">\n              <el-input v-model=\"form.orgName\" placeholder=\"请输入所属组织\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计划年份\" prop=\"planYear\">\n              <el-input v-model=\"form.planYear\" placeholder=\"请输入计划年份\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listLeader, getLeader, addLeader, updateLeader, delLeader,\n  exportLeader, batchExportLeader, downloadTemplate\n} from '@/api/performance/leader'\n\nexport default {\n  name: \"LeaderPlan\",\n  data() {\n    return {\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      list: [],\n      title: \"\",\n      open: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        taskType: null,\n        orgName: null,\n        planYear: null\n      },\n      form: {},\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"姓名长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"任务类型长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        performanceTask: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" },\n          { max: 1000, message: \"绩效任务长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        targetMeasures: [\n          { max: 1000, message: \"目标及措施长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        evaluationCriteria: [\n          { max: 1000, message: \"评价标准长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        responsibility: [\n          { max: 100, message: \"责任长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskCategory: [\n          { max: 100, message: \"责任分类长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        weightScore: [\n          { max: 50, message: \"权重分值长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { max: 100, message: \"完成时限长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        orgName: [\n          { max: 100, message: \"所属组织长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        planYear: [\n          { max: 10, message: \"计划年份长度不能超过10个字符\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/leader/import',\n      importParams: { planYear: '', orgName: '' },\n      uploadHeaders: {}\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listLeader(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        taskType: null,\n        performanceTask: null,\n        targetMeasures: null,\n        evaluationCriteria: null,\n        responsibility: null,\n        taskCategory: null,\n        weightScore: null,\n        deadline: null,\n        orgName: null,\n        planYear: null\n      };\n      this.resetForm(\"form\");\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加班子成员绩效计划\";\n    },\n    handleEdit(row) {\n      this.reset();\n      const id = row.id;\n      getLeader(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改班子成员绩效计划\";\n      }).catch(() => {\n        this.$modal.msgError(\"获取数据失败\");\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateLeader(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"修改失败\");\n            });\n          } else {\n            addLeader(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row ? [row.id] : this.ids;\n      const message = row\n        ? `是否确认删除\"${row.name}\"的绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条班子成员绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delLeader(ids.join(','));\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      // 如果只选择了一条数据，使用单个导出\n      if (this.ids.length === 1) {\n        const selectedRow = this.list.find(item => item.id === this.ids[0]);\n        this.handleExportSingle(selectedRow);\n        return;\n      }\n      // 多条数据使用批量导出\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      batchExportLeader(this.ids).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '班子成员绩效计划.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleExportSingle(row) {\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      exportLeader(row.id).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = `${row.name || '班子成员'}_绩效计划.docx`;\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    beforeImportUpload(file) {\n      const isDoc = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      if (!isDoc) {\n        this.$modal.msgError('只能上传Word文档格式文件!');\n      }\n      return isDoc;\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess('导入成功');\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    downloadTemplate() {\n      downloadTemplate().then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '班子成员绩效计划模板.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    }\n  }\n}\n</script> \n\n<style scoped>\n.upload-demo {\n  display: inline-block;\n  }\n  </style> "]}]}
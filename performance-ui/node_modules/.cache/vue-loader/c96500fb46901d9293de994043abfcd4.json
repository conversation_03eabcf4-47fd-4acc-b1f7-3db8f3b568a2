{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/index.vue?vue&type=style&index=0&id=0cde9ec5&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/index.vue", "mtime": 1754316341834}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5tYjggewogIG1hcmdpbi1ib3R0b206IDhweDsKfQoudXBsb2FkLWRlbW8gewogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6VA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          style=\"display: inline-block;\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"planList\"\n      @selection-change=\"handleSelectionChange\"\n      row-key=\"id\"\n      border\n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" width=\"120\"/>\n      <el-table-column label=\"任务来源\" align=\"center\" prop=\"taskSource\" width=\"120\"/>\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column label=\"责任科室\" align=\"center\" prop=\"responsibleDept\" width=\"120\"/>\n      <el-table-column label=\"分值及权重\" align=\"center\" prop=\"valueWeight\" width=\"120\"/>\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" width=\"120\"/>\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"deadline\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template #default=\"scope\">\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            icon=\"Edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['performance:plan:org:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            icon=\"Delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:plan:org:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"planRef\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"任务类型\" prop=\"taskType\">\n          <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n        </el-form-item>\n        <el-form-item label=\"任务来源\" prop=\"taskSource\">\n          <el-input v-model=\"form.taskSource\" placeholder=\"请输入任务来源\" />\n        </el-form-item>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n          <el-input v-model=\"form.responsibleDept\" placeholder=\"请输入责任科室\" />\n        </el-form-item>\n        <el-form-item label=\"分值及权重\" prop=\"valueWeight\">\n          <el-input v-model=\"form.valueWeight\" placeholder=\"请输入分值及权重\" />\n        </el-form-item>\n        <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n          <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n        </el-form-item>\n        <el-form-item label=\"完成时限\" prop=\"deadline\">\n          <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"cancel\">取 消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOrgPlan, getOrgPlan, addOrgPlan, updateOrgPlan, delOrgPlan } from '@/api/performance/org'\nimport { listPlan, getPlan, addPlan, updatePlan, delPlan, exportOrgPlan, batchExportOrgPlan, downloadOrgTemplate } from \"@/api/performance/plan\"\n\nexport default {\n  name: 'OrgPlan',\n  data() {\n    return {\n      planList: [],\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      formVisible: false,\n      formTitle: '',\n      form: {\n        id: undefined,\n        seq: '',\n        taskType: '',\n        taskSource: '',\n        performanceTask: '',\n        targetMeasures: '',\n        responsibleDept: '',\n        valueWeight: '',\n        responsibleLeader: '',\n        deadline: ''\n      },\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      title: '',\n      open: false,\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/plan/org/importExcel',\n      uploadHeaders: {},\n      rules: {\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" }\n        ],\n        taskSource: [\n          { required: true, message: \"任务来源不能为空\", trigger: \"blur\" }\n        ],\n        performanceTask: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" }\n        ],\n        targetMeasures: [\n          { required: true, message: \"目标及措施不能为空\", trigger: \"blur\" }\n        ],\n        responsibleDept: [\n          { required: true, message: \"责任科室不能为空\", trigger: \"blur\" }\n        ],\n        valueWeight: [\n          { required: true, message: \"分值及权重不能为空\", trigger: \"blur\" }\n        ],\n        responsibleLeader: [\n          { required: true, message: \"责任领导不能为空\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { required: true, message: \"完成时限不能为空\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true\n      listOrgPlan(this.queryParams).then(res => {\n        this.planList = res.rows || res\n        this.total = res.total || (res.length || 0)\n        this.loading = false\n      })\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.title = '新增绩效计划'\n      this.form = { id: undefined, seq: '', taskType: '', taskSource: '', performanceTask: '', targetMeasures: '', responsibleDept: '', valueWeight: '', responsibleLeader: '', deadline: '' }\n      this.open = true\n    },\n    handleEdit(row) {\n      this.title = '编辑绩效计划'\n      this.form = Object.assign({}, row)\n      this.open = true\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除绩效计划编号为\"' + ids + '\"的数据项？').then(function() {\n        return delOrgPlan(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条绩效计划数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        batchExportOrgPlan(this.ids).then(response => {\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '组织绩效计划_' + new Date().getTime() + '.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n          this.$modal.msgSuccess(\"导出成功\");\n        }).catch(() => {\n          this.$modal.closeLoading();\n          this.$modal.msgError(\"导出失败\");\n        });\n      }).catch(() => {});\n    },\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有绩效计划数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        exportOrgPlan().then(response => {\n          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '组织绩效计划.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n        }).catch(() => {\n          this.$modal.closeLoading();\n        });\n      }).catch(() => {});\n    },\n    handleDownloadTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍候...\");\n      downloadOrgExcelTemplate().then(response => {\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '组织绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getPlan(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改绩效计划\";\n      });\n    },\n    submitForm() {\n      this.$refs[\"planRef\"].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n            updatePlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    handleImportSuccess(response, file, fileList) {\n      if (response.code === 200 || response === '导入成功') {\n        this.$modal.msgSuccess('导入成功');\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                     file.type === 'application/vnd.ms-excel'\n      if (!isExcel) {\n        this.$message.error('只能上传Excel文件!')\n      }\n      return isExcel\n    },\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    reset() {\n      this.form = {\n        id: undefined,\n        seq: '',\n        taskType: '',\n        taskSource: '',\n        performanceTask: '',\n        targetMeasures: '',\n        responsibleDept: '',\n        valueWeight: '',\n        responsibleLeader: '',\n        deadline: ''\n      }\n      this.resetForm(\"planRef\")\n    },\n    formatJson(filterVal, jsonData) {\n      return jsonData.map(v => filterVal.map(j => v[j]))\n    }\n  }\n}\n</script>\n\n<style scoped>\n.mb8 {\n  margin-bottom: 8px;\n}\n.upload-demo {\n  display: inline-block;\n}\n</style>\n"]}]}
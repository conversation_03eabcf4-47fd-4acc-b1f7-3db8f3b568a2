<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleExportSelected">导出Word</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload
          class="upload-demo"
          :action="importUrl"
          :headers="uploadHeaders"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeImportUpload"
          :show-file-list="false"
          style="display: inline-block;">
          <el-button type="info" plain icon="el-icon-upload2" size="mini">导入Excel</el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-download" size="mini" @click="handleDownloadTemplate">下载模板</el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="planList"
      @selection-change="handleSelectionChange"
      row-key="id"
      border
      style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务类型" align="center" prop="taskType" width="120"/>
      <el-table-column label="任务来源" align="center" prop="taskSource" width="120"/>
      <el-table-column label="绩效任务" align="center" prop="performanceTask" min-width="200" show-overflow-tooltip/>
      <el-table-column label="目标及措施" align="center" prop="targetMeasures" min-width="200" show-overflow-tooltip/>
      <el-table-column label="责任科室" align="center" prop="responsibleDept" width="120"/>
      <el-table-column label="分值及权重" align="center" prop="valueWeight" width="120"/>
      <el-table-column label="责任领导" align="center" prop="responsibleLeader" width="120"/>
      <el-table-column label="完成时限" align="center" prop="deadline" width="120"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            size="small"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['performance:plan:org:edit']"
          >修改</el-button>
          <el-button
            size="small"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['performance:plan:org:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="planRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="任务类型" prop="taskType">
          <el-input v-model="form.taskType" placeholder="请输入任务类型" />
        </el-form-item>
        <el-form-item label="任务来源" prop="taskSource">
          <el-input v-model="form.taskSource" placeholder="请输入任务来源" />
        </el-form-item>
        <el-form-item label="绩效任务" prop="performanceTask">
          <el-input v-model="form.performanceTask" type="textarea" placeholder="请输入绩效任务" />
        </el-form-item>
        <el-form-item label="目标及措施" prop="targetMeasures">
          <el-input v-model="form.targetMeasures" type="textarea" placeholder="请输入目标及措施" />
        </el-form-item>
        <el-form-item label="责任科室" prop="responsibleDept">
          <el-input v-model="form.responsibleDept" placeholder="请输入责任科室" />
        </el-form-item>
        <el-form-item label="分值及权重" prop="valueWeight">
          <el-input v-model="form.valueWeight" placeholder="请输入分值及权重" />
        </el-form-item>
        <el-form-item label="责任领导" prop="responsibleLeader">
          <el-input v-model="form.responsibleLeader" placeholder="请输入责任领导" />
        </el-form-item>
        <el-form-item label="完成时限" prop="deadline">
          <el-input v-model="form.deadline" placeholder="请输入完成时限" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listOrgPlan, getOrgPlan, addOrgPlan, updateOrgPlan, delOrgPlan } from '@/api/performance/org'
import { listPlan, getPlan, addPlan, updatePlan, delPlan, exportOrgPlan, batchExportOrgPlan, downloadOrgExcelTemplate } from "@/api/performance/plan"

export default {
  name: 'OrgPlan',
  data() {
    return {
      planList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      formVisible: false,
      formTitle: '',
      form: {
        id: undefined,
        seq: '',
        taskType: '',
        taskSource: '',
        performanceTask: '',
        targetMeasures: '',
        responsibleDept: '',
        valueWeight: '',
        responsibleLeader: '',
        deadline: ''
      },
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      title: '',
      open: false,
      importUrl: process.env.VUE_APP_BASE_API + '/performance/plan/org/importExcel',
      uploadHeaders: {},
      rules: {
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "blur" }
        ],
        taskSource: [
          { required: true, message: "任务来源不能为空", trigger: "blur" }
        ],
        performanceTask: [
          { required: true, message: "绩效任务不能为空", trigger: "blur" }
        ],
        targetMeasures: [
          { required: true, message: "目标及措施不能为空", trigger: "blur" }
        ],
        responsibleDept: [
          { required: true, message: "责任科室不能为空", trigger: "blur" }
        ],
        valueWeight: [
          { required: true, message: "分值及权重不能为空", trigger: "blur" }
        ],
        responsibleLeader: [
          { required: true, message: "责任领导不能为空", trigger: "blur" }
        ],
        deadline: [
          { required: true, message: "完成时限不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList();
    // 设置上传认证头
    this.uploadHeaders = {
      Authorization: 'Bearer ' + this.$store.getters.token
    };
  },
  methods: {
    getList() {
      this.loading = true
      listOrgPlan(this.queryParams).then(res => {
        this.planList = res.rows || res
        this.total = res.total || (res.length || 0)
        this.loading = false
      })
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleAdd() {
      this.title = '新增绩效计划'
      this.form = { id: undefined, seq: '', taskType: '', taskSource: '', performanceTask: '', targetMeasures: '', responsibleDept: '', valueWeight: '', responsibleLeader: '', deadline: '' }
      this.open = true
    },
    handleEdit(row) {
      this.title = '编辑绩效计划'
      this.form = Object.assign({}, row)
      this.open = true
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除绩效计划编号为"' + ids + '"的数据项？').then(function() {
        return delOrgPlan(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExportSelected() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要导出的数据");
        return;
      }
      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条绩效计划数据项？').then(() => {
        this.$modal.loading("正在导出数据，请稍候...");
        batchExportOrgPlan(this.ids).then(response => {
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = '组织绩效计划_' + new Date().getTime() + '.docx';
          link.click();
          window.URL.revokeObjectURL(link.href);
          this.$modal.closeLoading();
          this.$modal.msgSuccess("导出成功");
        }).catch(() => {
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败");
        });
      }).catch(() => {});
    },
    handleExport() {
      this.$modal.confirm('是否确认导出所有绩效计划数据项？').then(() => {
        this.$modal.loading("正在导出数据，请稍候...");
        exportOrgPlan().then(response => {
          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = '组织绩效计划.docx';
          link.click();
          window.URL.revokeObjectURL(link.href);
          this.$modal.closeLoading();
        }).catch(() => {
          this.$modal.closeLoading();
        });
      }).catch(() => {});
    },
    handleDownloadTemplate() {
      this.$modal.loading("正在下载模板，请稍候...");
      downloadOrgExcelTemplate().then(response => {
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '组织绩效计划模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPlan(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改绩效计划";
      });
    },
    submitForm() {
      this.$refs["planRef"].validate(valid => {
        if (valid) {
          if (this.form.id) {
            updatePlan(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlan(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleImportSuccess(response, file, fileList) {
      if (response.code === 200 || response === '导入成功') {
        this.$modal.msgSuccess('导入成功');
        this.getList();
      } else {
        this.$modal.msgError(response.msg || '导入失败');
      }
    },
    handleImportError(error) {
      console.error("导入失败:", error);
      if (error.status === 401) {
        this.$modal.msgError("认证失败，请重新登录");
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/login';
        });
      } else {
        this.$modal.msgError("导入失败，请检查文件格式");
      }
    },
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
      }
      return isExcel
    },
    cancel() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        id: undefined,
        seq: '',
        taskType: '',
        taskSource: '',
        performanceTask: '',
        targetMeasures: '',
        responsibleDept: '',
        valueWeight: '',
        responsibleLeader: '',
        deadline: ''
      }
      this.resetForm("planRef")
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]))
    }
  }
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
.upload-demo {
  display: inline-block;
}
</style>

import request from '@/utils/request'

// 查询绩效计划列表
export function listPlan(query) {
  return request({
    url: '/performance/plan/org/list',
    method: 'get',
    params: query
  })
}

// 查询绩效计划详细
export function getPlan(id) {
  return request({
    url: '/performance/plan/org/' + id,
    method: 'get'
  })
}

// 新增绩效计划
export function addPlan(data) {
  return request({
    url: '/performance/plan/org',
    method: 'post',
    data: data
  })
}

// 修改绩效计划
export function updatePlan(data) {
  return request({
    url: '/performance/plan/org',
    method: 'put',
    data: data
  })
}

// 删除绩效计划
export function delPlan(id) {
  return request({
    url: '/performance/plan/org/' + id,
    method: 'delete'
  })
}

// 导出绩效计划
export function exportOrgPlan() {
  return request({
    url: '/performance/plan/org/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导出绩效计划
export function batchExportOrgPlan(ids) {
  return request({
    url: '/performance/plan/org/batchExport',
    method: 'post',
    data: ids,
    responseType: 'blob'
  })
}

// 下载Word模板
export function downloadOrgTemplate() {
  return request({
    url: '/performance/plan/org/downloadTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

// 下载Excel模板
export function downloadOrgExcelTemplate() {
  return request({
    url: '/performance/plan/org/downloadExcelTemplate',
    method: 'get',
    responseType: 'blob'
  })
}
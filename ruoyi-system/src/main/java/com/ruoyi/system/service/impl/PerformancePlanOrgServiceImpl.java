package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.PerformancePlanOrg;
import com.ruoyi.system.mapper.PerformancePlanOrgMapper;
import com.ruoyi.system.service.IPerformancePlanOrgService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

@Service
public class PerformancePlanOrgServiceImpl implements IPerformancePlanOrgService {
    @Autowired
    private PerformancePlanOrgMapper mapper;

    @Override
    public List<PerformancePlanOrg> selectPerformancePlanOrgList(PerformancePlanOrg performancePlanOrg) {
        return mapper.selectPerformancePlanOrgList(performancePlanOrg);
    }

    @Override
    public int insertPerformancePlanOrg(PerformancePlanOrg plan) {
        return mapper.insertPerformancePlanOrg(plan);
    }

    @Override
    public int insertBatch(List<PerformancePlanOrg> plans) {
        return mapper.insertBatch(plans);
    }

    @Override
    public int deletePerformancePlanOrgById(Long id) {
        return mapper.deletePerformancePlanOrgById(id);
    }

    @Override
    public int deletePerformancePlanOrgByIds(Long[] ids) {
        return mapper.deletePerformancePlanOrgByIds(ids);
    }

    @Override
    public int updatePerformancePlanOrg(PerformancePlanOrg plan) {
        return mapper.updatePerformancePlanOrg(plan);
    }

    @Override
    public PerformancePlanOrg selectPerformancePlanOrgById(Long id) {
        return mapper.selectPerformancePlanOrgById(id);
    }

    @Override
    public String importData(MultipartFile file) throws Exception {
        List<PerformancePlanOrg> plans = new ArrayList<>();
        XWPFDocument doc = new XWPFDocument(file.getInputStream());
        XWPFTable table = doc.getTables().get(0);
        // 占位提示内容
        String[] placeholders = {
            "1",
            "目标任务的性质或类别。",
            "目标任务主要来源或依据。",
            "描述重点工作任务内容。",
            "提出绩效任务目标和具体措施。",
            "牵头科室：XXX\n配合科室：XXX",
            "每项任务分值为100分，明确牵头和配合科室权重作为班子成员、科室绩效计划分配分值的依据。",
            "XXX",
            "202X年X月"
        };
        // 从第三行开始读取数据（跳过表头和提示行）
        for (int i = 1; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            // 跳过空行
            if (row.getCell(0).getText().trim().isEmpty()) {
                continue;
            }
            PerformancePlanOrg plan = new PerformancePlanOrg();
            // 序号
            String seqText = row.getCell(0).getText().trim();
            plan.setSeq(seqText.equals(placeholders[0]) ? null : Integer.parseInt(seqText));
            // 任务类型
            String taskType = row.getCell(1).getText().trim();
            plan.setTaskType(taskType.equals(placeholders[1]) ? null : taskType);
            // 任务来源
            String taskSource = row.getCell(2).getText().trim();
            plan.setTaskSource(taskSource.equals(placeholders[2]) ? null : taskSource);
            // 绩效任务
            String performanceTask = row.getCell(3).getText().trim();
            plan.setPerformanceTask(performanceTask.equals(placeholders[3]) ? null : performanceTask);
            // 目标及措施
            String targetMeasures = row.getCell(4).getText().trim();
            plan.setTargetMeasures(targetMeasures.equals(placeholders[4]) ? null : targetMeasures);
            // 责任科室
            String responsibleDept = row.getCell(5).getText().trim();
            plan.setResponsibleDept(responsibleDept.equals(placeholders[5]) ? null : responsibleDept);
            // 分值及权重
            String valueWeight = row.getCell(6).getText().trim();
            plan.setValueWeight(valueWeight.equals(placeholders[6]) ? null : valueWeight);
            // 责任领导
            String responsibleLeader = row.getCell(7).getText().trim();
            plan.setResponsibleLeader(responsibleLeader.equals(placeholders[7]) ? null : responsibleLeader);
            // 完成时限
            String deadline = row.getCell(8).getText().trim();
            plan.setDeadline(deadline.equals(placeholders[8]) ? null : deadline);
            plans.add(plan);
        }
        return mapper.insertBatch(plans) > 0 ? "导入成功" : "导入失败";
    }

    @Override
    public void exportWord(Long[] ids, HttpServletResponse response) throws Exception {
        List<PerformancePlanOrg> plans = new ArrayList<>();
        
        // 如果传入了ID，则导出指定数据；否则导出所有数据
        if (ids != null && ids.length > 0) {
            plans = mapper.selectPerformancePlanOrgListByIds(ids);
        } else {
            // 如果没有指定ID，导出所有数据
            plans = mapper.selectPerformancePlanOrgList(new PerformancePlanOrg());
        }
        
        // 读取模板
        InputStream templateStream = getClass().getResourceAsStream("/template/org.docx");
        if (templateStream == null) {
            throw new RuntimeException("组织绩效计划模板文件不存在");
        }
        
        XWPFDocument doc = new XWPFDocument(templateStream);
        List<XWPFTable> tables = doc.getTables();
        if (tables.isEmpty()) {
            throw new RuntimeException("模板文件中没有找到表格");
        }
        
        XWPFTable table = tables.get(0);
        
        // 找到数据插入位置：跳过表头和说明行
        int insertIndex = findDataInsertIndex(table);
        
        // 删除现有的数据行（保留表头和说明行）
        removeExistingDataRows(table, insertIndex);
        
        // 在指定位置插入数据行
        for (int i = 0; i < plans.size(); i++) {
            PerformancePlanOrg plan = plans.get(i);
            XWPFTableRow row = table.insertNewTableRow(insertIndex + i);
            
            // 确保有足够的单元格
            while (row.getTableCells().size() < 9) {
                row.createCell();
            }
            
            row.getCell(0).setText(plan.getSeq() != null ? String.valueOf(plan.getSeq()) : "");
            row.getCell(1).setText(plan.getTaskType() != null ? plan.getTaskType() : "");
            row.getCell(2).setText(plan.getTaskSource() != null ? plan.getTaskSource() : "");
            row.getCell(3).setText(plan.getPerformanceTask() != null ? plan.getPerformanceTask() : "");
            row.getCell(4).setText(plan.getTargetMeasures() != null ? plan.getTargetMeasures() : "");
            row.getCell(5).setText(plan.getResponsibleDept() != null ? plan.getResponsibleDept() : "");
            row.getCell(6).setText(plan.getValueWeight() != null ? plan.getValueWeight() : "");
            row.getCell(7).setText(plan.getResponsibleLeader() != null ? plan.getResponsibleLeader() : "");
            row.getCell(8).setText(plan.getDeadline() != null ? plan.getDeadline() : "");
        }
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=performance_plan_org.docx");
        
        // 输出文件
        doc.write(response.getOutputStream());
        doc.close();
    }
    
    /**
     * 找到数据插入的位置（跳过表头和说明行）
     */
    private int findDataInsertIndex(XWPFTable table) {
        int rowCount = table.getRows().size();
        
        // 从第二行开始查找（跳过表头）
        for (int i = 1; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            String rowText = row.getTableCells().stream()
                .map(cell -> cell.getText())
                .reduce("", (a, b) -> a + b);
            
            // 更保守的策略：只跳过明确的说明行和提示行
            if (rowText.contains("填写说明") || 
                rowText.contains("注意事项") || 
                rowText.contains("备注：") ||
                rowText.contains("说明：") ||
                rowText.contains("请在此行填写") || 
                rowText.contains("请在此处填写") ||
                rowText.contains("请填写相关内容") ||
                rowText.contains("示例：") ||
                rowText.contains("例如：") ||
                rowText.contains("目标任务的性质") || 
                rowText.contains("牵头科室")) {
                continue;
            }
            return i;
        }
        return rowCount;
    }
    
    /**
     * 删除现有的数据行（保留表头和说明行）
     */
    private void removeExistingDataRows(XWPFTable table, int startIndex) {
        int rowCount = table.getRows().size();
        
        // 从后往前删除，避免索引变化问题
        for (int i = rowCount - 1; i >= startIndex; i--) {
            XWPFTableRow row = table.getRow(i);
            String rowText = row.getTableCells().stream()
                .map(cell -> cell.getText())
                .reduce("", (a, b) -> a + b);
            
            // 更保守的删除策略：只删除明确的提示行
            boolean shouldDelete = false;
            
            // 只删除包含明确提示语的行
            if (rowText.contains("请在此行填写") || 
                rowText.contains("请在此处填写") ||
                rowText.contains("请填写相关内容") ||
                rowText.contains("示例：") ||
                rowText.contains("例如：") ||
                (rowText.contains("xxxx") && rowText.length() < 20) ||
                (rowText.contains("XXXX") && rowText.length() < 20) ||
                (rowText.contains("待填写") && rowText.length() < 20) ||
                (rowText.contains("请填写") && rowText.length() < 20)) {
                shouldDelete = true;
            }
            
            // 删除完全空的行（所有单元格都为空）
            if (rowText.trim().isEmpty()) {
                boolean allCellsEmpty = row.getTableCells().stream()
                    .allMatch(cell -> cell.getText().trim().isEmpty());
                if (allCellsEmpty) {
                    shouldDelete = true;
                }
            }
            
            // 执行删除
            if (shouldDelete) {
                table.removeRow(i);
            }
        }
    }
    


    @Override
    public void downloadTemplate(HttpServletResponse response) throws Exception {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/template/org.docx");
        if (templateStream == null) {
            throw new FileNotFoundException("模板文件不存在");
        }
        
        try (XWPFDocument document = new XWPFDocument(templateStream)) {
            // 清理模板中的占位符
            cleanTemplateDocument(document);
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=org_plan_template.docx");
            
            try (OutputStream outputStream = response.getOutputStream()) {
                document.write(outputStream);
                outputStream.flush();
            }
        }
    }
    
    /**
     * 清理模板文档中的占位符
     */
    private void cleanTemplateDocument(XWPFDocument document) {
        // 清理段落中的占位符
        document.getParagraphs().forEach(this::cleanParagraphPlaceholders);
        
        // 清理表格中的占位符
        document.getTables().forEach(table -> {
            table.getRows().forEach(row -> {
                row.getTableCells().forEach(cell -> {
                    cell.getParagraphs().forEach(this::cleanParagraphPlaceholders);
                });
            });
        });
    }
    
    /**
     * 清理段落中的占位符
     */
    private void cleanParagraphPlaceholders(XWPFParagraph paragraph) {
        paragraph.getRuns().forEach(run -> {
            String text = run.getText(0);
            if (text != null) {
                // 清理各种占位符
                text = text.replaceAll("\\$\\{.*?\\}", ""); // 清理所有占位符
                
                // 清理提示文本
                if (text.contains("请在此行填写") || 
                    text.contains("提示") || 
                    text.contains("参考") || 
                    text.contains("格式") || 
                    text.contains("要求") || 
                    text.contains("xxxx") || 
                    text.contains("XXXX") || 
                    text.contains("待填写") || 
                    text.contains("请填写")) {
                    text = "";
                }
                
                run.setText(text, 0);
            }
        });
    }

    @Override
    public List<PerformancePlanOrg> selectPerformancePlanOrgListByIds(Long[] ids) {
        if (ids == null || ids.length == 0) return new ArrayList<>();
        return mapper.selectPerformancePlanOrgListByIds(ids);
    }

    @Override
    public String importExcelData(MultipartFile file) throws Exception {
        List<PerformancePlanOrg> plans = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 从第二行开始读取数据（第一行是表头）
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 检查第一列是否为空，如果为空则跳过
                Cell firstCell = row.getCell(0);
                if (firstCell == null || getCellValue(firstCell).trim().isEmpty()) {
                    continue;
                }

                PerformancePlanOrg plan = new PerformancePlanOrg();

                // 根据Excel列顺序读取数据
                plan.setSeq(parseIntegerFromCell(row.getCell(0))); // 序号
                plan.setTaskType(getCellValue(row.getCell(1))); // 任务类型
                plan.setTaskSource(getCellValue(row.getCell(2))); // 任务来源
                plan.setPerformanceTask(getCellValue(row.getCell(3))); // 绩效任务
                plan.setTargetMeasures(getCellValue(row.getCell(4))); // 目标及措施
                plan.setResponsibleDept(getCellValue(row.getCell(5))); // 责任科室
                plan.setValueWeight(getCellValue(row.getCell(6))); // 分值及权重
                plan.setResponsibleLeader(getCellValue(row.getCell(7))); // 责任领导
                plan.setDeadline(getCellValue(row.getCell(8))); // 完成时限

                plans.add(plan);
            }
        }

        if (!plans.isEmpty()) {
            mapper.insertBatch(plans);
        }

        return "导入成功，共导入 " + plans.size() + " 条数据";
    }

    @Override
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/template/org-plan.xlsx");
        if (templateStream == null) {
            throw new FileNotFoundException("Excel模板文件不存在: /template/org-plan.xlsx");
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=org-plan-template.xlsx");

        // 直接将模板文件内容写入响应
        try (InputStream inputStream = templateStream;
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 从单元格解析整数
     */
    private Integer parseIntegerFromCell(Cell cell) {
        if (cell == null) return null;

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return (int) cell.getNumericCellValue();
                case STRING:
                    String value = cell.getStringCellValue().trim();
                    return value.isEmpty() ? null : Integer.parseInt(value);
                default:
                    return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
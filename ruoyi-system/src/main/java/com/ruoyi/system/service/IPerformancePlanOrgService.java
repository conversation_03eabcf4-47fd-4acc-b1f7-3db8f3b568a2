package com.ruoyi.system.service;

import com.ruoyi.system.domain.PerformancePlanOrg;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IPerformancePlanOrgService {
    /**
     * 查询组织绩效计划列表
     * 
     * @param performancePlanOrg 查询条件
     * @return 组织绩效计划列表
     */
    List<PerformancePlanOrg> selectPerformancePlanOrgList(PerformancePlanOrg performancePlanOrg);

    /**
     * 新增组织绩效计划
     * 
     * @param plan 组织绩效计划信息
     * @return 结果
     */
    int insertPerformancePlanOrg(PerformancePlanOrg plan);

    /**
     * 批量新增组织绩效计划
     * 
     * @param plans 组织绩效计划列表
     * @return 结果
     */
    int insertBatch(List<PerformancePlanOrg> plans);

    /**
     * 删除组织绩效计划
     * 
     * @param id 组织绩效计划ID
     * @return 结果
     */
    int deletePerformancePlanOrgById(Long id);

    /**
     * 批量删除组织绩效计划
     * 
     * @param ids 需要删除的组织绩效计划ID数组
     * @return 结果
     */
    int deletePerformancePlanOrgByIds(Long[] ids);

    /**
     * 修改组织绩效计划
     * 
     * @param plan 组织绩效计划信息
     * @return 结果
     */
    int updatePerformancePlanOrg(PerformancePlanOrg plan);

    /**
     * 查询组织绩效计划
     * 
     * @param id 组织绩效计划主键
     * @return 组织绩效计划
     */
    PerformancePlanOrg selectPerformancePlanOrgById(Long id);

    /**
     * 导入Word数据
     * 
     * @param file Word文件
     * @return 导入结果
     */
    String importData(MultipartFile file) throws Exception;

    /**
     * 导出Word数据
     * 
     * @param response HTTP响应
     */
    void exportWord(Long[] ids,HttpServletResponse response) throws Exception;

    /**
     * 下载Word模板
     * 
     * @param response HTTP响应
     */
    void downloadTemplate(HttpServletResponse response) throws Exception;

    /**
     * 查询组织绩效计划列表
     *
     * @param ids 组织绩效计划ID数组
     * @return 组织绩效计划列表
     */
    List<PerformancePlanOrg> selectPerformancePlanOrgListByIds(Long[] ids);

    /**
     * 导入Excel数据
     *
     * @param file Excel文件
     * @return 导入结果
     */
    String importExcelData(MultipartFile file) throws Exception;

    /**
     * 下载Excel模板
     *
     * @param response HTTP响应
     */
    void downloadExcelTemplate(HttpServletResponse response) throws Exception;
}
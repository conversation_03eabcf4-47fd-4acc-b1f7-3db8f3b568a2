# 组织绩效计划Excel导入功能说明

## 功能概述

已将组织绩效计划的导入功能从Word格式修改为Excel格式，提供更便捷的数据录入和批量导入功能。

## Excel模板格式

### 列顺序说明
| 列号 | 字段名称 | 说明 | 示例 |
|------|----------|------|------|
| A | 序号 | 数字序号 | 1, 2, 3... |
| B | 任务类型 | 任务的性质或类别 | 重点工作、常规工作 |
| C | 任务来源 | 任务的主要来源或依据 | 上级要求、年度计划 |
| D | 绩效任务 | 具体的工作任务内容 | 完成年度销售目标 |
| E | 目标及措施 | 实现目标的具体措施 | 制定销售策略，加强团队培训 |
| F | 责任科室 | 负责执行的科室 | 销售部、市场部 |
| G | 分值及权重 | 该任务的分值或权重 | 100分、20% |
| H | 责任领导 | 负责的领导 | 张三、李四 |
| I | 完成时限 | 任务完成的时间要求 | 2024年12月 |

### Excel模板示例
```
序号 | 任务类型 | 任务来源 | 绩效任务 | 目标及措施 | 责任科室 | 分值及权重 | 责任领导 | 完成时限
1    | 重点工作 | 年度计划 | 完成销售目标 | 制定销售策略 | 销售部 | 100分 | 张三 | 2024年12月
2    | 常规工作 | 日常管理 | 客户维护 | 定期回访客户 | 客服部 | 80分 | 李四 | 长期
```

## 使用说明

### 1. 下载模板
- 点击页面上的"下载模板"按钮
- 系统会下载一个名为"组织绩效计划模板.xlsx"的Excel文件
- 该文件包含标准的表头格式

### 2. 填写数据
- 在Excel模板中按照列顺序填写数据
- 第一行为表头，请勿修改
- 从第二行开始填写实际数据
- 序号列建议填写数字，便于排序

### 3. 导入数据
- 点击页面上的"导入Excel"按钮
- 选择填写好的Excel文件
- 系统会自动验证文件格式和数据
- 导入成功后会显示导入的记录数量

## 数据验证规则

### 必填字段
- 序号：建议填写，用于数据排序
- 任务类型：必填，不能为空
- 任务来源：必填，不能为空
- 绩效任务：必填，不能为空
- 目标及措施：必填，不能为空
- 责任科室：必填，不能为空
- 分值及权重：必填，不能为空
- 责任领导：必填，不能为空
- 完成时限：必填，不能为空

### 数据格式要求
- 序号：数字格式，可以为空
- 其他字段：文本格式，长度不超过数据库字段限制
- 不允许有完全空白的行（第一列为空则跳过该行）

## 技术实现

### 前端修改
1. **文件类型验证**
   ```javascript
   const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
   ```

2. **导入URL修改**
   ```javascript
   importUrl: process.env.VUE_APP_BASE_API + '/performance/plan/org/importExcel'
   ```

3. **模板下载**
   ```javascript
   downloadOrgExcelTemplate().then(response => {
     const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
     // ... 下载逻辑
   })
   ```

### 后端实现
1. **新增接口**
   - `POST /performance/plan/org/importExcel` - Excel导入
   - `GET /performance/plan/org/downloadExcelTemplate` - Excel模板下载

2. **Excel解析**
   - 使用Apache POI库解析Excel文件
   - 支持.xlsx格式文件
   - 自动跳过空行和无效数据

3. **数据处理**
   - 按列顺序读取数据
   - 自动类型转换和验证
   - 批量插入数据库

## 错误处理

### 常见错误及解决方案
1. **文件格式错误**
   - 错误信息：只能上传Excel文件
   - 解决方案：确保上传的是.xlsx或.xls格式的Excel文件

2. **数据格式错误**
   - 错误信息：导入失败，数据格式不正确
   - 解决方案：检查Excel中的数据格式，确保必填字段不为空

3. **文件为空**
   - 错误信息：请选择文件
   - 解决方案：选择一个有效的Excel文件

4. **网络错误**
   - 错误信息：导入失败，请检查网络连接
   - 解决方案：检查网络连接，重新尝试导入

## 注意事项

1. **文件大小限制**
   - 建议单次导入数据不超过1000条
   - 文件大小不超过10MB

2. **数据重复**
   - 系统不会自动去重
   - 重复导入会产生重复数据

3. **数据备份**
   - 导入前建议备份现有数据
   - 导入操作不可撤销

4. **权限要求**
   - 需要`performance:plan:org:import`权限才能导入
   - 需要`performance:plan:org:list`权限才能下载模板

## 性能优化

1. **批量处理**
   - 使用批量插入提高导入效率
   - 减少数据库连接次数

2. **内存管理**
   - 使用流式处理大文件
   - 及时释放资源

3. **错误恢复**
   - 导入失败时提供详细错误信息
   - 支持部分成功的数据导入

21:01:54.544 [Thread-42] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:01:54.583 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
21:01:54.586 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
21:01:55.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:01:55.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:02:00.285 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:02:00.286 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:02:01.107 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:02:01.107 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:02:01.112 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:02:01.558 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-8} inited
21:02:03.254 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.016 seconds (JVM running for 500530.506)
21:03:09.844 [Thread-46] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:03:09.850 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
21:03:09.855 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
21:03:10.510 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:03:10.510 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:03:11.016 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:03:11.016 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:03:11.019 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:03:11.345 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
21:03:12.644 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.179 seconds (JVM running for 500599.897)
21:07:53.111 [Thread-52] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:07:53.122 [Thread-52] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-9} closing ...
21:07:53.145 [Thread-52] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-9} closed
21:07:53.881 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:07:53.881 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:07:54.758 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:07:54.759 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:07:54.770 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:07:54.813 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:07:57.215 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:07:57.216 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:07:58.104 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:07:58.104 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:07:58.117 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:07:58.589 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-10} inited
21:08:01.355 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 4.196 seconds (JVM running for 500888.611)
21:39:42.160 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
21:39:42.170 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:39:47.970 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
21:49:01.963 [Thread-56] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:01.971 [Thread-56] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-10} closing ...
21:49:01.975 [Thread-56] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-10} closed
21:49:02.890 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:02.891 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:03.384 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:03.384 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:03.389 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:03.716 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-11} inited
21:49:05.189 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.354 seconds (JVM running for 502943.473)
21:49:12.096 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:49:39.599 [Thread-63] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:39.601 [Thread-63] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-11} closing ...
21:49:39.604 [Thread-63] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-11} closed
21:49:40.100 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:40.100 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:40.539 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:40.539 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:40.542 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:40.807 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-12} inited
21:49:41.857 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.776 seconds (JVM running for 502980.134)
21:49:46.536 [Thread-67] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:46.541 [Thread-67] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-12} closing ...
21:49:46.554 [Thread-67] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-12} closed
21:49:47.052 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:47.053 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:47.462 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:47.462 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:47.465 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:47.731 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-13} inited
21:49:48.865 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.845 seconds (JVM running for 502987.141)
21:49:59.298 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:50:16.739 [Thread-71] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:50:16.742 [Thread-71] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-13} closing ...
21:50:16.747 [Thread-71] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-13} closed
21:50:17.247 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:50:17.247 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:50:17.728 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:50:17.728 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:50:17.731 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:50:18.029 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-14} inited
21:50:20.578 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.362 seconds (JVM running for 503018.854)
21:50:31.968 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:06:38.673 [Thread-75] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:06:38.674 [Thread-75] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-14} closing ...
22:06:38.677 [Thread-75] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-14} closed
22:06:39.294 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:06:39.294 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:06:39.750 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:06:39.750 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:06:39.753 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:06:40.135 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-15} inited
22:06:41.538 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.268 seconds (JVM running for 503999.818)
22:06:54.379 [Thread-79] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:06:54.384 [Thread-79] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-15} closing ...
22:06:54.391 [Thread-79] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-15} closed
22:06:55.062 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:06:55.062 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:06:55.444 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:06:55.444 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:06:55.448 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:06:55.726 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-16} inited
22:06:57.039 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.998 seconds (JVM running for 504015.32)
22:07:12.244 [Thread-83] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:07:12.254 [Thread-83] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-16} closing ...
22:07:12.258 [Thread-83] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-16} closed
22:07:12.956 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:07:12.956 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:07:13.614 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:07:13.614 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:07:13.617 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:07:13.866 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-17} inited
22:07:15.064 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.129 seconds (JVM running for 504033.345)
22:07:46.797 [Thread-87] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:07:46.800 [Thread-87] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-17} closing ...
22:07:46.804 [Thread-87] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-17} closed
22:07:47.429 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:07:47.429 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:07:47.979 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:07:47.979 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:07:47.983 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:07:48.490 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-18} inited
22:07:49.990 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.58 seconds (JVM running for 504068.271)
22:08:02.699 [Thread-91] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:08:02.704 [Thread-91] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-18} closing ...
22:08:02.709 [Thread-91] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-18} closed
22:08:03.466 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:08:03.467 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:08:04.084 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:08:04.084 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:08:04.089 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:08:04.350 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-19} inited
22:08:05.432 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.008 seconds (JVM running for 504083.713)
22:08:36.770 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:10:24.413 [Thread-95] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:10:24.415 [Thread-95] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-19} closing ...
22:10:24.417 [Thread-95] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-19} closed
22:10:25.094 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:10:25.094 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:10:25.591 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:10:25.591 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:10:25.596 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:10:25.953 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-20} inited
22:10:27.493 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.42 seconds (JVM running for 504225.775)
22:10:29.186 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:12:54.813 [Thread-99] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:12:54.814 [Thread-99] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-20} closing ...
22:12:54.825 [Thread-99] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-20} closed
22:12:55.500 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:12:55.501 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:12:55.933 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:12:55.933 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:12:55.938 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:12:56.280 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-21} inited
22:12:57.761 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.285 seconds (JVM running for 504376.044)
22:13:02.938 [Thread-103] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:13:02.945 [Thread-103] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-21} closing ...
22:13:02.949 [Thread-103] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-21} closed
22:13:04.270 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:13:04.270 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:13:04.754 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:13:04.754 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:13:04.758 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:13:05.005 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-22} inited
22:13:06.293 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.056 seconds (JVM running for 504384.576)
22:13:58.737 [Thread-107] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:13:58.742 [Thread-107] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-22} closing ...
22:13:58.746 [Thread-107] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-22} closed
22:13:59.672 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:13:59.673 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:14:00.527 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:14:00.527 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:14:00.541 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:14:01.021 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-23} inited
22:14:04.096 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 4.489 seconds (JVM running for 504442.379)
22:14:04.377 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:14:05.304 [Thread-111] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:14:05.313 [Thread-111] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-23} closing ...
22:14:05.317 [Thread-111] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-23} closed
22:14:06.088 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:14:06.089 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:14:06.946 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:14:06.946 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:14:06.951 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:14:07.246 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-24} inited
22:14:08.867 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.822 seconds (JVM running for 504447.15)
22:14:28.383 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:20:02.549 [Thread-115] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:20:02.552 [Thread-115] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-24} closing ...
22:20:02.555 [Thread-115] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-24} closed
22:20:03.166 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:20:03.167 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:20:03.725 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:20:03.725 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:20:03.733 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:20:03.996 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-25} inited
22:20:05.380 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.235 seconds (JVM running for 504803.717)
22:20:09.748 [Thread-119] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:20:09.751 [Thread-119] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-25} closing ...
22:20:09.756 [Thread-119] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-25} closed
22:20:10.530 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:20:10.530 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:20:10.901 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:20:10.901 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:20:10.904 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:20:11.145 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-26} inited
22:20:12.479 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.972 seconds (JVM running for 504810.816)
